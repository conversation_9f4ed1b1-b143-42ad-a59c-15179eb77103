import { defineUniPages } from '@uni-helper/vite-plugin-uni-pages'

export default defineUniPages({
  // 你也可以定义 pages 字段，它具有最高的优先级。
  pages: [
    {
      path: 'pages/index/index',
    },
  ],
  globalStyle: {
    navigationBarBackgroundColor: '@navBgColor',
    navigationBarTextStyle: '@navTxtStyle',
    navigationBarTitleText: 'uniapp-vite-template',
    backgroundColor: '#F7F7F5',
    backgroundTextStyle: '@bgTxtStyle',
    backgroundColorTop: '@bgColorTop',
    backgroundColorBottom: '@bgColorBottom',
    navigationStyle: 'custom',
  },
  easycom: {
    custom: {
      '^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)':
        'z-paging/components/z-paging$1/z-paging$1.vue',
    },
  },
  tabBar: {
    // 支付宝小程序自定义需要特殊处理
    custom: true,
    color: `#CCCCCC`,
    selectedColor: `#4A8EFE`,
    backgroundColor: `#FFFFFF`,
    borderStyle: 'white',
    list: [
      {
        pagePath: 'pages/index/index',
        text: '首页',
        iconPath: '/static/tabbar/home.png',
        selectedIconPath: '/static/tabbar/home-select.png',
      },
      {
        pagePath: 'pages/workbench/workbench',
        text: '工作台',
        iconPath: '/static/tabbar/workbench.png',
        selectedIconPath: '/static/tabbar/workbench-select.png',
      },
      {
        pagePath: 'pages/monitor/monitor',
        text: '监控中心',
        iconPath: '/static/tabbar/monitor.png',
        selectedIconPath: '/static/tabbar/monitor-select.png',
      },
      {
        pagePath: 'pages/my/my',
        text: '我的',
        iconPath: '/static/tabbar/my.png',
        selectedIconPath: '/static/tabbar/my-select.png',
      },
    ],
  },
})
