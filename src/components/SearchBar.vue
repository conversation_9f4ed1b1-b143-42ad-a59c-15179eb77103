<template>
  <view class="search-section p-3 pb-2 flex items-center">
    <view
      class="search-input-wrapper flex-1 bg-white rounded-md pl-2 pr-1 py-1 flex items-center shadow-sm"
    >
      <text class="i-carbon-search text-[#9A9A9A] mr-2"></text>
      <input
        :value="keyword"
        @input="onInput"
        class="flex-1 text-sm bg-transparent focus:outline-none mr-2"
        :placeholder="placeholder"
        placeholder-class="text-[#9A9A9A] text-xs"
        confirm-type="search"
        @confirm="onSearch"
      />
      <text
        v-if="keyword"
        class="i-carbon-close-filled text-[#9A9A9A] mr-2"
        @click="clearKeyword"
      ></text>
      <button
        class="search-button bg-primary text-white text-sm font-medium px-4 py-1.5 rounded-md active:opacity-80 shadow-sm flex-shrink-0"
        @click="onSearch"
      >
        搜索
      </button>
    </view>
    <view v-if="showFilter" class="filter-icon ml-2 f-c-c" @click="onFilterClick">
      <text class="i-carbon-filter text-lg text-[#666]"></text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

const modelValue = defineModel<string>('modelValue', { default: '' })

defineProps({
  placeholder: {
    type: String,
    default: '请输入关键字',
  },
  showFilter: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['search', 'filter'])

const keyword = ref(modelValue.value)

// 监听modelValue变化
watch(
  () => modelValue.value,
  newVal => {
    keyword.value = newVal
  },
)

// 处理输入
const onInput = (e: any) => {
  const value = e.detail.value
  keyword.value = value
  modelValue.value = value
}

// 清空关键词
const clearKeyword = () => {
  keyword.value = ''
  modelValue.value = ''
  emit('search', '')
}

// 处理搜索
const onSearch = () => {
  emit('search', keyword.value)
}

// 处理过滤按钮点击
const onFilterClick = () => {
  emit('filter')
}
</script>

<style scoped>
.search-section {
  width: 100%;
  box-sizing: border-box;
}

.search-input-wrapper {
  border: 1px solid #eee;
}

.filter-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
</style>
