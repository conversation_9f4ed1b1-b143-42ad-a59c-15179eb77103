{"swagger": "2.0", "info": {"description": "外部系统调用网关", "version": "1.0", "title": "日日顺乐农商户平台网关API接口", "contact": {"name": "rrsjk", "url": "http://www.rrsjk.com", "email": "<EMAIL>"}}, "host": "operation.xiaoxianglink.com", "basePath": "/hdsapi", "paths": {"/light/operation/app/user/info": {"get": {"tags": ["光伏运维/app/用户管理"], "summary": "获取当前登录用户信息", "description": "获取当前登录用户信息", "operationId": "getUserInfoUsingGET", "produces": ["*/*"], "parameters": [{"name": "Authorization", "in": "header", "description": "访问令牌,格式Bearer ${access_token}", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ExecuteResult«UserInfo»"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}, "deprecated": false}}}, "UserInfo": {"type": "object", "properties": {"dept": {"type": "string", "description": "用户部门名称"}, "name": {"type": "string", "description": "用户姓名"}, "phone": {"type": "string", "description": "用户手机号"}, "roleName": {"type": "string", "description": "用户角色名称"}, "subCenterUser": {"type": "boolean", "description": "是否为分中心用户"}}, "title": "UserInfo"}}