// @/api/station.ts
import type { PaginatedContent } from '@/service/types'
import type { Station, StationGetByStationCodeParams, StationPageParams } from '@/types/api/Station'
import { getInstance } from '@/service'
import { useUserStore } from '@/store/modules/user'

/**
 * 分页查询电站列表
 */
export async function getStationPage(
  params: StationPageParams,
): Promise<PaginatedContent<Station>> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap: Record<string, string> = {
    haier: '/light/operation/station/page',
    merchant: '/light/operation/station/page',
  }
  const path = urlMap[userType || 'haier']
  return await getInstance().get(path, { params })
}

/**
 * 根据电站编码获取电站详情
 */
export async function getStationByStationCode(
  params: StationGetByStationCodeParams,
): Promise<Station> {
  const userStore = useUserStore()
  const userType = userStore.userInfo?.userType
  const urlMap: Record<string, string> = {
    haier: '/light/operation/station/getByStationCode',
    merchant: '/light/operation/station/getByStationCode',
  }
  const path = urlMap[userType || 'haier']
  return await getInstance().get(path, { params })
}
