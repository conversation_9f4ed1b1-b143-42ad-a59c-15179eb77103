/**
 * @description 用户信息
 */
export interface UserInfo {
  /**
   * 用户部门名称
   */
  dept?: string
  /**
   * 用户姓名
   */
  name?: string
  /**
   * 用户手机号
   */
  phone?: string
  /**
   * 用户角色名称
   */
  roleName?: string
  /**
   * 是否为分中心用户
   */
  subCenterUser?: boolean
  /**
   * 用户类型
   */
  userType: 'merchant' | 'haier'
  /**
   * 当前用户是否为运维商
   */
  isProvider?: boolean
}

/**
 * @description 验证短信并注册服务兵 - 请求参数
 */
export interface RegisterParams {
  /**
   * 验证码
   */
  code: string
  /**
   * 确认密码
   */
  confirm_password: string
  /**
   * 手机号
   */
  mobile: string
  /**
   * 密码
   */
  password: string
}

/**
 * @description 发送注册短信验证码 - 请求参数
 */
export interface SendRegisterSmsParams {
  /**
   * 手机号
   */
  mobile: string
}

/**
 * @description 发送忘记密码短信验证码 - 请求参数
 */
export interface SendUserForgetPasswordSmsParams {
  /**
   * 手机号
   */
  mobile: string
  /**
   * 用户类型
   */
  userType?: 'merchant' | 'haier'
}

/**
 * @description 重置用户密码 - 请求参数
 */
export interface UserResetPasswordParams {
  /**
   * 手机号
   */
  mobile: string
  /**
   * 验证码
   */
  code: string
  /**
   * 新密码
   */
  password: string
  /**
   * 确认新密码
   */
  confirm_password: string
  /**
   * 用户类型
   */
  userType?: 'merchant' | 'haier'
}
