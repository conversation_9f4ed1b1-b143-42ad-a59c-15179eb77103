<script setup lang="ts">
import { useMessage, useToast } from 'wot-design-uni'

const message = useMessage()
const toast = useToast()

function goToVersionPage() {
  uni.navigateTo({
    url: '/pages/setting/version',
  })
}

async function clearCache() {
  try {
    await message.confirm({
      title: '提示',
      msg: '确定要清除应用缓存吗？这将清除所有应用保存的文件。',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    })

    let storageClearStatus = ''
    let filesClearStatus = ''
    let finalToastIcon: 'success' | 'none' = 'success'

    uni.getSavedFileList({
      success: (savedFilesRes) => {
        const filesToRemove = savedFilesRes.fileList
        if (filesToRemove.length === 0) {
          filesClearStatus = '无已保存文件'
          const toastMessage = storageClearStatus ? `${storageClearStatus}；${filesClearStatus}` : filesClearStatus
          if (finalToastIcon === 'success') {
            toast.success({ msg: toastMessage, duration: 2500 })
          } else {
            toast.show({ msg: toastMessage, duration: 2500 })
          }
          return
        }

        let filesClearedCount = 0
        let filesFailedCount = 0
        let operationsCompleted = 0

        filesToRemove.forEach((fileEntry) => {
          uni.removeSavedFile({
            filePath: fileEntry.filePath,
            complete: (removeRes) => {
              operationsCompleted++
              if (removeRes.errMsg === 'removeSavedFile:ok') {
                filesClearedCount++
              } else {
                filesFailedCount++
                console.error('删除文件失败:', fileEntry.filePath, removeRes.errMsg)
              }

              if (operationsCompleted === filesToRemove.length) {
                if (filesFailedCount > 0) {
                  filesClearStatus = `文件: ${filesClearedCount} 清除, ${filesFailedCount} 失败`
                  if (finalToastIcon === 'success') finalToastIcon = 'none'
                } else {
                  filesClearStatus = `所有 ${filesClearedCount} 个已保存文件已清除`
                }
                const toastMessage = storageClearStatus ? `${storageClearStatus}；${filesClearStatus}` : filesClearStatus
                if (finalToastIcon === 'success') {
                  toast.success({ msg: toastMessage, duration: 3000 })
                } else {
                  toast.show({ msg: toastMessage, duration: 3000 })
                }
              }
            },
          })
        })
      },
      fail: (getListErr) => {
        console.error('获取已保存文件列表失败:', getListErr)
        filesClearStatus = '获取文件列表失败'
        finalToastIcon = 'none'
        const toastMessage = storageClearStatus ? `${storageClearStatus}；${filesClearStatus}` : filesClearStatus
        toast.show({ msg: toastMessage, duration: 2500 })
      },
    })
  } catch (error) {
    // 用户点击了取消按钮或关闭了对话框
    console.log('用户取消清除缓存')
  }
}
</script>

<template>
  <div class="setting-page">
    <wd-cell-group border>
      <wd-cell title="关于版本" is-link custom-class="setting-item" @click="goToVersionPage" />
      <wd-cell title="清除缓存" is-link custom-class="setting-item" @click="clearCache" />
      <wd-cell title="注销账户" is-link custom-class="setting-item" />
    </wd-cell-group>
  </div>
</template>

<route lang="json">{
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "系统设置"
  }
}</route>

<style scoped lang="scss">
.setting-page {
  background-color: white;
  min-height: 100vh;
}

:deep(.setting-item) {
  .wd-cell__title {
    color: #303133;
    font-size: 15px;
  }

  .wd-cell__arrow {
    color: #c0c4cc;
  }
}
</style>
