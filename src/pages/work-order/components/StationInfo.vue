<script setup lang="ts">
import { ref } from 'vue'
import type { WorkOrder } from '@/types/api/Workorder'
import { pmSpecialFlag, businessType, isWarranty, opType } from '@/constants/dict'

defineProps<{
  station: WorkOrder
  title?: string
}>()

const isStationInfoExpanded = ref(false)
const toggleStationInfo = () => {
  isStationInfoExpanded.value = !isStationInfoExpanded.value
}
</script>

<template>
  <view class="card m-3 p-4 bg-white rounded-lg shadow-sm">
    <view class="card-title flex items-center mb-3">
      <view class="accent-bar bg-primary w-1 h-4 mr-2"></view>
      <text class="text-sm font-bold text-[#4B4B4B]">{{ title || '电站信息' }}</text>
    </view>
    <view class="detail-row">
      <text class="label">电站编码</text>
      <text class="value">{{ station.stationCode || '-' }}</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">逆变器SN码</text>
      <text class="value">{{ station.inverterSn || '-' }}</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">业主姓名</text>
      <text class="value">{{ station.stationName || '-' }}</text>
    </view>
    <view class="detail-row mt-1">
      <text class="label">手机号</text>
      <text class="value">{{ station.stationPhone || '-' }}</text>
    </view>

    <view v-show="isStationInfoExpanded">
      <view class="detail-row mt-1">
        <text class="label">电站模式</text>
        <text class="value">{{ station.stationMode || '-' }}</text>
      </view>
      <view class="detail-row mt-1">
        <text class="label">关联资方</text>
        <text class="value">{{
          (station.specialFlag && pmSpecialFlag[station.specialFlag]) || '-'
        }}</text>
      </view>
      <view class="detail-row mt-1">
        <text class="label">所属分中心</text>
        <text class="value">{{ station.subCenterName || '-' }}</text>
      </view>
      <view class="detail-row mt-1">
        <text class="label">运维商类别</text>
        <text class="value">{{ (station.opType && opType[station.opType]) || '-' }}</text>
      </view>
      <view class="detail-row mt-1">
        <text class="label">运维商名称</text>
        <text class="value">{{ station.opName || '-' }}</text>
      </view>
      <view class="detail-row mt-1">
        <text class="label">业务类型</text>
        <text class="value">{{
          (station.businessType && businessType[station.businessType]) || '-'
        }}</text>
      </view>
      <view class="detail-row mt-1">
        <text class="label">是否质保期内</text>
        <text class="value">{{ station.isWarranty && isWarranty[station.isWarranty] }}</text>
      </view>
      <view class="detail-row mt-1">
        <text class="label">区域</text>
        <text class="value">{{
          [station.provinceName, station.cityName, station.regionName]
            .filter(Boolean)
            .join('') || '-'
        }}</text>
      </view>
      <view class="detail-row mt-1">
        <text class="label">详细地址</text>
        <text class="value">{{ station.address || '-' }}</text>
      </view>
    </view>
    <view class="toggle-section flex items-center justify-center mt-3 pt-3 border-t border-gray-200 cursor-pointer"
      @click="toggleStationInfo">
      <text class="text-sm text-primary mr-1">{{
        isStationInfoExpanded ? '收起信息' : '展开更多'
      }}</text>
      <wd-icon :name="isStationInfoExpanded ? 'arrow-up' : 'arrow-down'" size="16px" color="#3B82F6"></wd-icon>
    </view>
  </view>
</template>

<style scoped>
.detail-row {
  display: flex;
  font-size: 14px;
  line-height: 20px;
}

.label {
  color: #666;
  width: 88px;
}

.value {
  color: #333;
  text-align: right;
  word-break: break-all;
}
</style>
