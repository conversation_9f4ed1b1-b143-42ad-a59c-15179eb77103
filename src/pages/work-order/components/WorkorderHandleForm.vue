<script setup lang="ts">
import { ref, computed } from 'vue'
import type { FormItemConfig } from '@/components/DynamicForm.vue'
import DynamicForm from '@/components/DynamicForm.vue'
import FileUpload from '@/components/FileUpload.vue'
import type { WorkOrderHandleReq } from '@/types/api/Workorder'
import { getValueByPath, setValueByPath } from '@/utils/path'

const props = defineProps({
  editable: {
    type: Boolean,
    default: true,
  },
})

// 使用defineModel替代手动实现的双向绑定
const formData = defineModel<WorkOrderHandleReq>('formData', {
  default: () => ({ remark: '' }),
})

// 转换检查项为动态表单配置
const formConfig = computed<FormItemConfig[]>(() => {
  return [
    ...(formData.value.handleCheckItems?.map((item, index) => {
      const baseConfig: FormItemConfig = {
        field: `handleCheckItems.${index}.resultContent`,
        label: item.checkItem || '',
        required: true,
        disabled: !props.editable,
        type: 'input',
      }

      // 根据检查项类型配置不同的表单项
      const resultType = item.resultType?.toLowerCase() || ''

      if (resultType === 'text') {
        return {
          ...baseConfig,
          type: 'input',
          placeholder: '请输入',
        }
      } else if (resultType === 'select') {
        return {
          ...baseConfig,
          type: 'select',
          options: [
            { label: '良好', value: '良好' },
            { label: '损坏', value: '损坏' },
          ],
        }
      } else if (resultType === 'image') {
        return {
          ...baseConfig,
          type: 'custom',
          customSlot: 'image-upload',
          attrs: {
            extraData: item,
          },
        }
      }
      return baseConfig
    }) || []),
    {
      field: 'remark',
      label: '备注',
      disabled: !props.editable,
      type: 'textarea',
      placeholder: '请输入备注信息',
      attrs: {
        maxlength: 500,
        showWordLimit: true,
      },
    },
  ] as FormItemConfig[]
})

// 表单引用
const dynamicFormRef = ref()

// 向父组件暴露方法
defineExpose({
  validate: async () => {
    if (dynamicFormRef.value) {
      return await dynamicFormRef.value.validateWithHandleError()
    }
    return false
  },
})

// 预览图片
const previewImage = (url: string) => {
  if (!url) return
  uni.previewImage({
    urls: [url],
  })
}
</script>

<template>
  <DynamicForm v-model="formData" :config="formConfig" ref="dynamicFormRef">
    <!-- 图片上传插槽 -->
    <template #image-upload="{ model, field, extra }">
      <view class="image-check-container">
        <!-- 查找对应的原始检查项 -->
        <view class="image-section" v-if="extra.exampleImage">
          <view class="image-section-label">示例照片</view>
          <wd-img
            :src="extra.exampleImage"
            mode="aspectFill"
            custom-class="example-image"
            @click="previewImage(extra.exampleImage || '')"
          />
        </view>
        <view class="image-section">
          <view class="image-section-label">
            上传照片 <text class="text-xs text-gray-400 ml-1">(最多3张)</text>
          </view>
          <FileUpload
            :file-list="getValueByPath(model, field)"
            @update:file-list="val => setValueByPath(model, field, val)"
            :disabled="!editable"
            :limit="3"
            watermarkText="123"
          />
        </view>
      </view>
    </template>
  </DynamicForm>
</template>

<style scoped>
.image-check-container {
  display: flex;
  gap: 16px;
}

.image-section {
  text-align: left;
  .image-section-label {
    font-size: 14px;
    color: #606266;
    margin-bottom: 8px;
    width: 100%;
  }
}

.example-image {
  width: 100px;
  height: 100px;
  border-radius: 6px;
  background-color: #f5f7fa;
  border: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  box-sizing: border-box;
}
</style>
