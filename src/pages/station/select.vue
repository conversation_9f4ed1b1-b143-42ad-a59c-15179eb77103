<script lang="ts" setup>
import { ref, getCurrentInstance } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { getStationPage } from '@/api/station'
import type { Station, StationPageParams } from '@/types/api/Station'

const keyword = ref('')

const paging = ref<ZPagingRef>()
const stationList = ref<Station[]>([])
const pageSize = ref(10)

const onSearch = () => {
  paging.value?.reload()
}

const clearKeyword = () => {
  keyword.value = ''
  onSearch()
}

const queryList = async (pageNum: number, pSize: number) => {
  const params: StationPageParams = {
    pageNum,
    pageSize: pSize,
  }
  if (keyword.value) {
    if (/^\d{11}$/.test(keyword.value)) {
      params.phone = keyword.value
    } else if (/^\d*$/.test(keyword.value)) {
      params.stationCode = keyword.value
    } else {
      params.name = keyword.value
    }
  }

  try {
    const res = await getStationPage(params)
    paging.value?.completeByTotal(res.content, res.totalElements)
  } catch (error) {
    paging.value?.complete(false)
  }
}

let pageEventChannel: UniApp.EventChannel | undefined

onLoad(() => {
  const instance = getCurrentInstance()
  if (instance?.proxy) {
    // 使用 as any 来绕过类型检查，因为 proxy 的类型 ComponentPublicInstance 不直接包含 getOpenerEventChannel
    const proxy = instance.proxy as any
    if (typeof proxy.getOpenerEventChannel === 'function') {
      pageEventChannel = proxy.getOpenerEventChannel()
    } else {
      console.warn('getOpenerEventChannel is not a function on the current page instance proxy.')
    }
  } else {
    console.warn('Could not get current instance proxy in onLoad.')
  }
})

const selectStation = (station: Station) => {
  if (pageEventChannel) {
    pageEventChannel.emit('selectStation', { stationCode: station.stationCode })
    uni.navigateBack()
  } else {
    console.warn(
      'pageEventChannel is not available. Navigating back without emitting selectStation event.',
    )
    // 如果 eventChannel 不可用，仍然返回，但不会传递数据
    uni.navigateBack()
  }
}
</script>

<template>
  <view class="monitor-page">
    <search-bar
      v-model="keyword"
      @search="onSearch"
      placeholder="请输入电站编号/户主姓名/手机号码"
    />

    <z-paging
      ref="paging"
      v-model="stationList"
      class="station-list-paging"
      :fixed="false"
      @query="queryList"
      :default-page-size="pageSize"
      :auto-hide-loading-after-first-loaded="false"
      :show-loading-more-when-reload="true"
      use-virtual-list
    >
      <template #empty>
        <view class="station-list-paging__empty-message">暂无监控数据</view>
      </template>
      <view class="station-list-content">
        <view
          v-for="item in stationList"
          :key="item.id || item.stationCode"
          class="station-card"
          @click="selectStation(item)"
        >
          <view class="card-header">
            <text class="title">{{ item.stationCode }}</text>
          </view>
          <view class="card-meta">
            <text>{{ item.createdAt }}</text>
          </view>
          <view class="card-body">
            <view class="detail-item">
              <text class="label">电站名称</text>
              <text class="value">{{ item.name }}</text>
            </view>
            <view class="detail-item">
              <text class="label">电站地址</text>
              <text class="value">{{ item.address }}</text>
            </view>
            <view class="detail-item">
              <text class="label">联系方式</text>
              <text class="value">{{ item.phone }}</text>
            </view>
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "选择电站"
  }
}
</route>

<style scoped lang="scss">
.monitor-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;

  .search-section {
    background-color: #f7f7f5;
    padding: 12px;
    padding-bottom: 8px;
    display: flex;
    align-items: center;

    .search-input-wrapper {
      flex: 1 1 0%;
      background-color: #fff;
      border-radius: 6px;
      padding-left: 8px;
      padding-right: 4px;
      padding-top: 4px;
      padding-bottom: 4px;
      display: flex;
      align-items: center;
      box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);

      .search-input-wrapper__icon-search {
        color: #9a9a9a;
        margin-right: 8px;
      }

      .search-input-wrapper__field {
        flex: 1 1 0%;
        font-size: 14px;
        background-color: transparent;
        margin-right: 8px;
        border: none;

        &:focus {
          outline: 2px solid transparent;
          outline-offset: 2px;
        }
      }

      .search-input-wrapper__icon-clear {
        color: #9a9a9a;
        font-size: 18px;
        margin-left: 4px;
        margin-right: 4px;
        cursor: pointer;

        &:active {
          opacity: 0.8;
        }
      }

      .search-button {
        background-color: var(--wot-color-primary, #007aff);
        color: #fff;
        font-size: 14px;
        font-weight: 500;
        padding: 6px 16px;
        border-radius: 6px;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        flex-shrink: 0;
        border: none;
        line-height: 1.5;

        &:active {
          opacity: 0.8;
        }
      }
    }
  }

  .station-list-paging {
    flex: 1;

    .station-list-paging__empty-message {
      padding: 16px;
      text-align: center;
      color: #6b7280;
    }
  }

  .station-list-content {
    padding: 8px 12px;
  }

  .station-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
      }
    }

    .card-meta {
      font-size: 12px;
      color: #999;
      margin-bottom: 10px;
      border-bottom: 1px solid #f0f0f0;
      padding-bottom: 8px;
    }

    .card-body {
      .detail-item {
        display: flex;
        font-size: 14px;
        line-height: 1.6;
        margin-bottom: 4px;

        .label {
          color: #666;
          margin-right: 8px;
          flex-shrink: 0;
          width: 70px;
        }

        .value {
          color: #333;
          word-break: break-all;
          flex: 1;
        }
      }
    }

    &:last-child {
      margin-bottom: 0px;
    }
  }
}

:deep(.uni-input-placeholder) {
  color: #9a9a9a;
  font-size: 12px;
}
</style>
