<template>
  <view class="user-info-page">
    <wd-cell-group border>
      <wd-cell title="姓名" :value="userInfo.name" />
      <wd-cell title="手机号码" :value="userInfo.phone" />
      <wd-cell title="所属部门" :value="userInfo.department" />
      <wd-cell title="角色" :value="userInfo.role" />
    </wd-cell-group>
  </view>
</template>

<script setup lang="ts">
import { reactive } from 'vue'

interface UserProfile {
  name: string
  phone: string
  department: string
  role: string
}

const userInfo = reactive<UserProfile>({
  name: '张师傅',
  phone: '138****5678',
  department: '智能技术部',
  role: '技术主管',
})
</script>

<route lang="json">{
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "个人信息"
  }
}</route>

<style scoped lang="scss">
.user-info-page {
  background-color: white;
  min-height: 100vh;
}
</style>
