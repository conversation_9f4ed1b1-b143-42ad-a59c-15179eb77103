<script setup lang="ts">
import { login, getAppUserInfo } from '@/api'
import { useAuthStore, useUserStore } from '@/store'
import { ref } from 'vue'
import type { FormInstance, FormRules } from 'wot-design-uni/components/wd-form/types'

defineOptions({
  name: 'LoginPage',
})

const authStore = useAuthStore()
const userStore = useUserStore()
const activeTab = ref(0)
const formData = ref({
  username: '15251083185',
  password: 'hAIER123456',
})
const formRef = ref<FormInstance>()

const rules: FormRules = {
  username: [{ required: true, message: '请输入账号' }],
  password: [{ required: true, message: '请输入密码' }],
}

async function handleLogin() {
  if (!formRef.value) return

  try {
    const { valid } = await formRef.value.validate()
    if (valid) {
      const userType = activeTab.value === 0 ? 'haier' : 'merchant'
      login({
        username: formData.value.username,
        password: formData.value.password,
        userType: userType,
      })
        .then(async res => {
          authStore.setToken(res.access_token)
          const userInfo = await getAppUserInfo(userType)
          userStore.setUserInfo({
            userType,
            ...userInfo,
          })
          uni.showToast({ title: '登录成功' })
          uni.switchTab({ url: '/pages/index/index' })
        })
        .catch(err => {
          console.log(err)
          uni.showToast({ title: '登录失败', icon: 'none' })
        })
    }
  } catch (error) {
    console.error('Validation error:', error)
    uni.showToast({ title: '表单校验失败', icon: 'none' })
  }
}

function goToRegister() {
  uni.navigateTo({
    url: '/pages/user/register',
  })
}

function goToForgotPassword() {
  uni.showToast({ title: '跳转忘记密码页', icon: 'none' })
}

function goToAgreement(type: 'service' | 'privacy') {
  uni.showToast({ title: `查看${type === 'service' ? '服务协议' : '隐私政策'}`, icon: 'none' })
}
</script>

<template>
  <view class="login-page h-screen flex flex-col overflow-hidden">
    <view class="login-page__header relative h-300px">
      <view class="bg absolute inset-0 z-0" />
      <view class="logo-container absolute left-30px top-113px z-1">
        <image src="/static/logo.png" class="logo w-72px h-72px rounded-17px" mode="aspectFit" />
      </view>
    </view>

    <view class="login-page__tabs flex items-center px-30px mb-8">
      <view
        class="tab-item mr-30px"
        :class="{ 'tab-item--active': activeTab === 0 }"
        @click="activeTab = 0"
      >
        <text class="text-16px font-medium"> 海尔用户 </text>
      </view>
      <view
        class="tab-item"
        :class="{ 'tab-item--active': activeTab === 1 }"
        @click="activeTab = 1"
      >
        <text class="text-16px font-medium"> 运维商用户 </text>
      </view>
    </view>

    <view class="login-page__form flex-1 px-30px">
      <wd-form ref="formRef" :model="formData" :rules="rules">
        <wd-cell-group border>
          <wd-input
            v-model="formData.username"
            prop="username"
            label-width="0"
            placeholder="请输入账号"
            clearable
            size="large"
            placeholder-style="font-size: 15px; color: #8F959E;"
            input-style="font-size: 15px;"
          />
          <wd-input
            v-model="formData.password"
            prop="password"
            label-width="0"
            showPassword
            placeholder="请输入密码"
            clearable
            size="large"
            placeholder-style="font-size: 15px; color: #8F959E;"
            input-style="font-size: 15px;"
          >
          </wd-input>
        </wd-cell-group>
      </wd-form>

      <view class="flex justify-end mt-15px">
        <text class="text-14px text-[#8F959E]" @click="goToForgotPassword"> 忘记密码 </text>
      </view>

      <wd-button
        type="primary"
        block
        size="large"
        custom-class="mt-40px !h-50px !rounded-15px"
        @click="handleLogin"
      >
        登录
      </wd-button>

      <view class="text-center mt-25px" v-if="activeTab === 1">
        <text class="text-14px text-[#8F959E]"> 没有账号？ </text>
        <text class="text-14px text-primary" @click="goToRegister"> 立即注册 </text>
      </view>
    </view>

    <view class="login-page__agreement text-center text-14px py-20px">
      <text class="text-[#8F959E]"> 登录代表您已同意 </text>
      <text class="text-primary" @click="goToAgreement('service')"> 服务协议 </text>
      <text class="text-[#8F959E]"> 和 </text>
      <text class="text-primary" @click="goToAgreement('privacy')"> 隐私政策 </text>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "登录"
  }
}
</route>

<style scoped lang="scss">
.login-page {
  background-color: white;

  &__header {
    .bg {
      background: linear-gradient(103deg, #ffede5 0%, #b9daff 81%);

      &::after {
        content: '';
        position: absolute;
        inset: 0;
        background: linear-gradient(180deg, rgba(255, 255, 255, 0.0001) 0%, #ffffff 95%);
      }
    }
  }

  &__tabs {
    .tab-item {
      color: #8f959e;
      padding-bottom: 8px;
      border-bottom: 2px solid transparent;
      transition: all 0.3s ease;

      &--active {
        color: #2f2f2f;
        border-bottom-color: #2887ff;
      }
    }
  }

  &__form {
    :deep(.wd-input) {
      padding: 15px 0;
      background-color: transparent;
    }

    :deep(.wd-cell-group) {
      background-color: transparent;

      &::after {
        border: none;
      }
    }

    :deep(.wd-cell) {
      padding: 0 !important;
      margin: 0 !important;
      background-color: transparent !important;

      &::after {
        border-bottom: 1px solid #d0d3d5;
        left: 0px !important;
      }
    }

    :deep(.wd-input__label) {
      display: none;
    }
  }

  &__agreement {
    .text-primary {
      color: #2887ff;
    }
  }
}
</style>
