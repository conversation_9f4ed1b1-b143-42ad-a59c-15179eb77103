<script lang="ts" setup>
import { ref } from 'vue'
import { getStationPage } from '@/api/station'
import type { Station, StationPageParams } from '@/types/api/Station'

const keyword = ref('')

const paging = ref<ZPagingRef>()
const stationList = ref<Station[]>([])
const pageSize = ref(10)

const onSearch = () => {
  paging.value?.reload()
}

const queryList = async (pageNum: number, pSize: number) => {
  const params: StationPageParams = {
    pageNum,
    pageSize: pSize,
  }
  if (keyword.value) {
    if (/^\d{11}$/.test(keyword.value)) {
      params.phone = keyword.value
    } else {
      params.name = keyword.value
      params.stationCode = keyword.value
    }
  }

  try {
    const res = await getStationPage(params)
    paging.value?.completeByTotal(res.content, res.totalElements);
  } catch (error) {
    paging.value?.complete(false)
  }
}

function handleNavigate(item: Station) {
  if (item.longitude && item.latitude) {
    uni.openLocation({
      longitude: Number(item.longitude),
      latitude: Number(item.latitude),
      name: item.name || '电站位置',
      address: item.address || '',
    });
  } else {
    uni.showToast({ title: '电站位置信息不完整', icon: 'none' });
  }
}

</script>

<template>
  <view class="monitor-page">
    <!-- 搜索区域 -->
    <view class="search-section p-3 pb-2 flex items-center">
      <view class="search-input-wrapper flex-1 bg-white rounded-md pl-2 pr-1 py-1 flex items-center shadow-sm">
        <text class="i-carbon-search text-[#9A9A9A] mr-2"></text>
        <input v-model="keyword" class="flex-1 text-sm bg-transparent focus:outline-none mr-2"
          placeholder="请输入电站编号/户主姓名/手机号码" placeholder-class="text-[#9A9A9A] text-xs" confirm-type="search"
          @confirm="onSearch" />
        <button
          class="search-button bg-primary text-white text-sm font-medium px-4 py-1.5 rounded-md active:opacity-80 shadow-sm flex-shrink-0"
          @click="onSearch">搜索</button>
      </view>
    </view>

    <!-- 电站列表 -->
    <z-paging ref="paging" v-model="stationList" class="station-list-paging" :fixed="false" @query="queryList"
      :default-page-size="pageSize" :auto-hide-loading-after-first-loaded="false" :show-loading-more-when-reload="true"
      use-virtual-list>
      <template #empty>
        <view class="p-4 text-center text-gray-500">暂无监控数据</view>
      </template>
      <view class="station-list-content">
        <view v-for="item in stationList" :key="item.id || item.stationCode" class="station-card">
          <view class="card-header">
            <text class="title">{{ item.stationCode }}</text>
            <!-- <button class="navigate-button" @click.stop="handleNavigate(item)">
              <text class="i-carbon-location"></text>
              导航
            </button> -->
          </view>
          <view class="card-meta">
            <text>{{ item.createdAt }}</text>
          </view>
          <view class="card-body">
            <view class="detail-item">
              <text class="label">电站名称</text>
              <text class="value">{{ item.name }}</text>
            </view>
            <view class="detail-item">
              <text class="label">电站地址</text>
              <text class="value">{{ item.address }}</text>
            </view>
            <view class="detail-item">
              <text class="label">联系方式</text>
              <text class="value">{{ item.phone }}</text>
            </view>
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<route lang="json">{
  "style": {
    "navigationStyle": "default",
    "navigationBarTitleText": "监控中心"
  }
}</route>

<style scoped lang="scss">
.monitor-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;

  .search-section {
    background-color: #F7F7F5;
  }

  .filter-icon {
    background-color: #fff;
    width: 38px;
    height: 38px;
    border-radius: 6px;
  }

  .station-list-paging {
    flex: 1;
  }

  .station-list-content {
    padding: 8px 12px;
  }

  .station-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
      }

      .navigate-button {
        background-color: rgba(55, 172, 254, 0.1);
        color: #366CFD;
        font-size: 12px;
        padding: 4px 10px;
        display: flex;
        align-items: center;
        border: none;
        line-height: 1.5;

        .i-carbon-location {
          margin-right: 4px;
        }

        &:active {
          opacity: 0.8;
        }
      }
    }

    .card-meta {
      font-size: 12px;
      color: #999;
      margin-bottom: 10px;
      border-bottom: 1px solid #F0F0F0;
      padding-bottom: 8px;
    }

    .card-body {
      .detail-item {
        display: flex;
        font-size: 14px;
        line-height: 1.6;
        margin-bottom: 4px;

        .label {
          color: #666;
          margin-right: 8px;
          flex-shrink: 0;
          width: 70px;
        }

        .value {
          color: #333;
          word-break: break-all;
          flex: 1;
        }
      }
    }

    &:last-child {
      margin-bottom: 0px;
    }
  }

  .filter-popup-content {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
}

:deep(.uni-input-placeholder) {
  color: #9A9A9A;
  font-size: 12px;
}
</style>
