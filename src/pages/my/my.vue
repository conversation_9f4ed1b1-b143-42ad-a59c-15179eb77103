<script lang="ts" setup>
import { useUserStore, useAuthStore } from '@/store'
import { ref } from 'vue'
import { useMessage } from 'wot-design-uni'

const userStore = useUserStore()
const userInfo = userStore.userInfo
const auth = useAuthStore()
const message = useMessage()

const commonFunctions = ref([
  { name: '个人信息', icon: 'i-carbon-user-profile' }, // Mapping DSL icon
  { name: '修改密码', icon: 'i-carbon-edit' }, // Mapping DSL icon
])

const moreServices = ref([
  { name: '智能客服', icon: 'i-carbon-headset' }, // Mapping DSL icon
  { name: '模拟考试', icon: 'i-carbon-task' }, // Mapping DSL icon, assuming "前往答题" means exam/quiz
])

function handleFunctionClick(item: { name: string }) {
  uni.showToast({ title: `点击了 ${item.name}`, icon: 'none' })
}

function handleLogoutAccount() {
  uni.showToast({ title: '点击了 注销账号', icon: 'none' })
  // Add actual logout logic here
}

function handleLogout() {
  message
    .confirm({
      title: '提示',
      msg: '确定要退出登录吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
    })
    .then(() => {
      auth.cleanToken()
      uni.reLaunch({ url: '/pages/user/login' })
    })
}
</script>

<template>
  <view class="my-page min-h-screen">
    <!-- Header Section -->
    <view class="header relative">
      <view class="header__gradient-bg absolute inset-0" />
      <!-- Adjusted header content padding/positioning -->
      <view class="header__content relative z-1 flex items-center pt-[98px] px-4 pb-4">
        <!-- Avatar adjusted to 50x50 -->
        <image :src="'/static/logo.png'" class="header__avatar w-[50px] h-[50px] mr-4" />
        <view class="header__info">
          <text class="header__name block">{{ userInfo?.name }}</text>
          <!-- Signature color ensured white, removed opacity -->
          <text class="header__signature block mt-1">{{ userInfo?.phone }}</text>
        </view>
      </view>
    </view>

    <!-- Content Section - Positioned relative to the top -->
    <view class="content-section px-4 absolute top-[170px] left-0 right-0 z-2">
      <!-- Common Functions Card - Precise padding and custom shadow class -->
      <view class="card common-functions-card mb-4 p-[15px] card-shadow rounded-lg bg-white">
        <!-- Title color adjusted -->
        <text class="card__title block mb-4 text-sm font-medium text-[#4B4B4B]">常用功能</text>
        <view class="card__grid grid grid-cols-4 gap-4">
          <view
            v-for="item in commonFunctions"
            :key="item.name"
            class="card__grid-item flex flex-col items-center"
            @click="handleFunctionClick(item)"
          >
            <!-- Icon and text color adjusted -->
            <view class="card__icon text-3xl mb-1" :class="item.icon" />
            <text class="card__text text-xs">{{ item.name }}</text>
          </view>
        </view>
      </view>

      <!-- More Services Card - Assuming similar structure and precise styles -->
      <view class="card more-services-card mb-4 p-[15px] card-shadow rounded-lg bg-white">
        <!-- Title color adjusted -->
        <text class="card__title block mb-4 text-sm font-medium text-[#4B4B4B]">更多服务</text>
        <view class="card__grid grid grid-cols-4 gap-4">
          <view
            v-for="item in moreServices"
            :key="item.name"
            class="card__grid-item flex flex-col items-center"
            @click="handleFunctionClick(item)"
          >
            <!-- Icon and text color adjusted -->
            <view class="card__icon text-3xl mb-1" :class="item.icon" />
            <text class="card__text text-xs">{{ item.name }}</text>
          </view>
        </view>
      </view>

      <!-- Action List - Precise shadow and rounding, custom class for icons -->
      <view class="action-list rounded-lg overflow-hidden card-shadow bg-white">
        <wd-cell-group border>
          <wd-cell title="注销账号" is-link @click="handleLogoutAccount">
          </wd-cell>
          <wd-cell title="退出登录" is-link @click="handleLogout">
          </wd-cell>
        </wd-cell-group>
      </view>
    </view>
  </view>
</template>

<route lang="json">
{
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "我的"
  },
  "usingComponents": {
    "wd-cell": "wot-design-uni/components/wd-cell/wd-cell",
    "wd-cell-group": "wot-design-uni/components/wd-cell-group/wd-cell-group",
    "wd-icon": "wot-design-uni/components/wd-icon/wd-icon"
  }
}
</route>

<style scoped lang="scss">
.my-page {
  background-color: #f5f7fa; // DSL paint_121:4365
}

.header {
  color: white;

  &__gradient-bg {
    background: linear-gradient(180deg, #4b8dfe 0%, #f5f7fa 100%); // DSL paint_121:6323
    height: 283px; // DSL 121:4576 height
  }

  &__avatar {
    /* Styles w-[50px] h-[50px] mr-4 applied in template */
    border-color: #f7f7f5; // DSL paint_89:6718
    border-radius: 9999px; // rounded-full
    border-width: 2px; // border-2
  }

  &__name {
    @apply text-lg font-medium;
    /* Match DSL font_145:5871 (18px Medium) */
  }

  &__signature {
    @apply text-xs mt-1;
    /* Match DSL font_145:5878 (12px Regular) */
    color: white;
    /* Ensure color is white */
  }
}

.card {
  /* Base styles applied in template: bg-white rounded-lg p-[15px] mb-4 */
  &.card-shadow {
    box-shadow: 5px 5px 10px 5px rgba(0, 0, 0, 0.05);
    /* DSL effect_121:4370 */
  }

  &__title {
    /* text-sm font-medium text-[#4B4B4B] applied in template */
  }

  &__grid-item {
    cursor: pointer;

    &:active {
      opacity: 0.7;
    }
  }

  &__icon {
    /* text-3xl mb-1 applied in template */
    color: #4b4b4b; // DSL paint_77:0910
  }

  &__text {
    /* text-xs applied in template */
    color: #4b4b4b; // DSL paint_77:0910
  }
}

.action-list {
  /* Base styles applied in template: rounded-lg overflow-hidden bg-white */
  &.card-shadow {
    // Reuse card shadow class
    box-shadow: 5px 5px 10px 5px rgba(0, 0, 0, 0.05);
    /* DSL effect_121:4370 */
  }

  .action-list__icon {
    @apply mr-2 text-lg;
    color: #4b4b4b; // DSL paint_123:2351
  }

  :deep(.wd-cell__title) {
    color: #4b4b4b; // DSL paint_77:0910
    font-size: 14px; // DSL font_145:5889
    line-height: 2;
  }

  :deep(.wd-cell) {
    padding: 0px 16px; // Adjusted vertical padding to match DSL (12px)
  }
}

// Make status bar text dark on this page if header is light near top
/* #ifdef APP-PLUS */
:deep(uni-page-head) .uni-page-head__title {
  color: #000;
}

/* #endif */
</style>
