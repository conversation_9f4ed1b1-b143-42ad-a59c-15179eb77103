# UniApp Vite 模板项目架构指南

## 项目概述

这是一个基于 UniApp + Vue 3 + TypeScript + Vite 的跨平台应用开发模板，支持多端开发部署，这个项目主要是 App 平台。

## 技术栈

- **框架**: Vue 3.5.13 + UniApp
- **构建工具**: Vite 5.2.x
- **语言**: TypeScript 5.7.3
- **包管理**: pnpm
- **状态管理**: Pinia 2.0.36
- **UI 组件库**:
  - uni-ui 1.5.7
  - wot-design-uni 1.7.1
  - wot-design-uni 文档地址: https://wot-design-uni.netlify.app/
  - wot-design-uni 组件使用说明: https://wot-design-uni.netlify.app/component/button.html
  - wot-design-uni github 地址: https://github.com/Moonofweisheng/wot-design-uni
- **数据请求**: @tanstack/vue-query 4.37.1 + @uni-helper/uni-network 0.20.0
- **工具库**:
  - @vueuse/core 12.7.0
  - dayjs 1.11.13
  - qs 6.5.3
- **CSS 预处理**: Sass
- **原子化 CSS**: UnoCSS

## 项目结构

```
├── .clinerules/          # 项目规则文档
├── build/                # 构建相关配置
│   ├── config/           # 构建配置
│   └── plugins/          # Vite 插件配置
├── src/                  # 源代码
│   ├── api/              # API 接口定义
│   ├── composables/      # 组合式函数
│   ├── constants/        # 常量定义
│   ├── layouts/          # 布局组件
│   ├── pages/            # 页面组件
│   ├── plugins/          # 插件配置
│   ├── service/          # 服务层
│   ├── static/           # 静态资源
│   ├── store/            # Pinia 状态管理
│   ├── styles/           # 全局样式
│   ├── uni_modules/      # UniApp 模块
│   └── utils/            # 工具函数
├── typings/              # 类型定义
└── ...配置文件           # 各类配置文件
```

## 多环境配置

项目支持多环境配置，包括：

- 开发环境 (development)
- 生产环境 (production)
- 预发布环境 (staging)

环境变量文件：

- `.env`：所有环境通用配置
- `.env.development`：开发环境配置
- `.env.production`：生产环境配置

## 多平台支持

项目支持以下平台的开发和构建：

- App (iOS/Android)

## 开发命令

### 开发模式

```bash
# H5 开发
pnpm dev:h5

# App 开发
pnpm dev:app
```

### 构建命令

```bash
# H5 构建
pnpm build:h5

# App 构建
pnpm build:app
```

## 代码规范

项目集成了以下代码规范工具：

- ESLint：JavaScript/TypeScript 代码检查
- Stylelint：CSS/SCSS 样式检查
- Prettier：代码格式化
- commitlint：Git 提交信息规范

## Git 工作流

项目使用 simple-git-hooks 和 lint-staged 进行提交前代码检查，使用 commitizen (cz-git) 规范化提交信息。

提交代码推荐使用：

```bash
pnpm commit
```

## 项目特性

1. **自动路由**：使用 @uni-helper/vite-plugin-uni-pages 自动生成路由配置
2. **自动导入**：使用 unplugin-auto-import 自动导入 API
3. **布局系统**：使用 @uni-helper/vite-plugin-uni-layouts 支持布局系统
4. **组件自动注册**：使用 @uni-helper/vite-plugin-uni-components 自动注册组件
5. **图标系统**：使用 unplugin-icons 支持图标系统
6. **原子化 CSS**：使用 UnoCSS 支持原子化 CSS
7. **类型安全**：完整的 TypeScript 类型支持

## 最佳实践

1. 使用组合式 API
2. 使用 Pinia 进行状态管理
3. 使用 vue-query 处理异步数据和缓存
4. 使用 composables 抽象可复用逻辑
5. 遵循项目的文件命名和组织约定
6. 使用环境变量区分开发/生产配置
7. vue 文件中使用 `<script setup lang="ts">` `<template>` `<route>` 和 `<style scoped lang="scss">` 语法，同时也按照这个顺序编写代码，先 script，再 template，route，最后是 style
8. 页面布局和内容优先使用 UniApp 的基础组件 `view`, `text`, 和 `image` 标签进行构建，以保证最佳的跨端兼容性和性能。仅在基础组件无法满足复杂交互或特定 UI 需求时，才考虑使用更复杂的组件库或自定义组件。

## 注意事项

1. 不同平台可能存在兼容性差异，请参考 UniApp 文档
2. 使用条件编译处理平台差异 `#ifdef` 和 `#endif`
3. App 开发需要配置原生插件和权限

## 业务代码

1. 以下组件优先使用 wot-design-uni:
   - Popup
   - Segmented
   - Badge
   - Form
   - Button: round 设置为 false
2. 列表使用 z-paging 组件。
3. 调用 uniapp 方法，优先使用`@uni-helper/uni-promises`中的方法

## wot-design-uni 组件
1. 引用 wot-design-uni 组件库的组件时，不需要在 router 区块中使用 usingComponents。
2. wot-design-uni 组件库的变量在 `.clinerules/wot-design-variable.scss` 中定义，使用前请先查看相关的变量。

## CSS 类名和 UnoCSS 的使用建议

1.  **优先使用 BEM 规范**：为具有明确结构和意义的组件或元素（及其子元素）创建自定义的 BEM 风格 CSS 类（例如，`.block__element--modifier`，使用小写字母和短横线）。
2.  **自定义类优先于原子类**：如果一个元素已经应用了自定义的 BEM 类，并且该类的样式在 `<style>` 块中定义，则**不应**再在该元素上直接使用 UnoCSS 原子类来定义那些本应由其 BEM 类管理的样式。应将这些原子类对应的样式整合到 `<style>` 块中相应 BEM 类的 CSS 定义中。
3.  **UnoCSS 的适用场景**：
    *   **图标系统**：可结合 `unplugin-icons` 提供的原子图标类（如 `i-carbon-search`）和自定义的 BEM 类。原子图标类提供图标本身，BEM 类（如 `.search-input__icon`）用于调整该图标实例的特定样式（如颜色、边距），这些特定样式应在 `<style>` 块中定义。
    *   **纯工具性、非结构化样式**：对于没有合适 BEM 类覆盖的、非常局部的、纯粹工具性的样式（例如，一个临时的、一次性的边距调整，且不适合为其创建新的 BEM 类或修改现有 BEM 类），可以谨慎使用 UnoCSS 原子类。
    *   **层级较深或简单布局辅助**：在 BEM 命名层级可能变得过深（例如，超过两级 `block__element-subelement`）或需要非常简单的布局辅助（如快速实现 flex 容器但不值得为其创建完整 BEM 结构）时，可考虑使用 UnoCSS 原子类以提高开发效率，但仍需注意避免过度使用导致模板混乱。
4.  **移除未使用的或冗余的 CSS**：
    *   如果定义了自定义 CSS 类，但该类未在模板中使用，或者其样式未在 `<style>` 块中定义，并且它没有和子节点形成有效的 BEM 分组，则应删除该类。
    *   如果 `<input>` 等元素的 `placeholder` 样式已通过全局 `:deep()` 或组件内的 CSS 规则统一定义，则应移除模板中相应的 `placeholder-class` 属性，以避免样式冗余。
5.  **样式单位统一**：在 `<style scoped lang="scss">` 块中，所有长度单位应统一使用 `px`。
6.  **注释规范**：遵循 `.clinerules/biz_rules.md` 中关于注释的总体原则。在 CSS 中，避免添加解释显而易见样式或单位转换过程的注释。代码和类名本身应尽可能自明。

**目标**：在 BEM 的结构化、可维护性与 UnoCSS 的便利性、开发效率之间取得平衡，编写清晰、一致且易于维护的样式代码。
